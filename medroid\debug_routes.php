<?php

/**
 * Route Debugging Script for Medroid Application
 * 
 * This script helps debug and fix the missing route issue:
 * "The route web-api/chat/start could not be found."
 */

echo "=== MEDROID ROUTE DEBUGGING SCRIPT ===\n\n";

// Check if we're in Laravel environment
if (!function_exists('app')) {
    echo "Error: This script must be run in Laravel environment.\n";
    echo "Run with: php artisan tinker < debug_routes.php\n";
    exit(1);
}

try {
    echo "1. CHECKING ROUTE CACHE STATUS\n";
    echo "==============================\n";
    
    // Check if routes are cached
    $routeCachePath = base_path('bootstrap/cache/routes-v7.php');
    if (file_exists($routeCachePath)) {
        echo "✓ Routes are CACHED (this might be the issue)\n";
        echo "Cache file: {$routeCachePath}\n";
        echo "Cache modified: " . date('Y-m-d H:i:s', filemtime($routeCachePath)) . "\n\n";
        
        echo "SOLUTION: Clear route cache with:\n";
        echo "php artisan route:clear\n\n";
    } else {
        echo "✓ Routes are NOT cached\n\n";
    }
    
    echo "2. CHECKING CURRENT ROUTES\n";
    echo "==========================\n";
    
    // Get all registered routes
    $routes = app('router')->getRoutes();
    $webApiChatRoutes = [];
    $allRoutes = [];
    
    foreach ($routes as $route) {
        $uri = $route->uri();
        $methods = implode('|', $route->methods());
        $name = $route->getName() ?: 'unnamed';
        $action = $route->getActionName();
        
        $allRoutes[] = [
            'uri' => $uri,
            'methods' => $methods,
            'name' => $name,
            'action' => $action
        ];
        
        // Look for web-api/chat routes specifically
        if (strpos($uri, 'web-api/chat') !== false) {
            $webApiChatRoutes[] = [
                'uri' => $uri,
                'methods' => $methods,
                'name' => $name,
                'action' => $action
            ];
        }
    }
    
    echo "Total routes registered: " . count($allRoutes) . "\n";
    echo "Web-API Chat routes found: " . count($webApiChatRoutes) . "\n\n";
    
    if (!empty($webApiChatRoutes)) {
        echo "FOUND WEB-API CHAT ROUTES:\n";
        foreach ($webApiChatRoutes as $route) {
            echo "- {$route['methods']} {$route['uri']} -> {$route['action']}\n";
        }
        echo "\n";
    } else {
        echo "❌ NO WEB-API CHAT ROUTES FOUND!\n\n";
    }
    
    echo "3. CHECKING SPECIFIC ROUTE\n";
    echo "==========================\n";
    
    // Check if the specific route exists
    $targetRoute = 'web-api/chat/start';
    $found = false;
    
    foreach ($allRoutes as $route) {
        if ($route['uri'] === $targetRoute && strpos($route['methods'], 'POST') !== false) {
            echo "✓ FOUND: POST {$targetRoute}\n";
            echo "  Action: {$route['action']}\n";
            echo "  Name: {$route['name']}\n";
            $found = true;
            break;
        }
    }
    
    if (!$found) {
        echo "❌ ROUTE NOT FOUND: POST {$targetRoute}\n\n";
        
        echo "4. SEARCHING FOR SIMILAR ROUTES\n";
        echo "===============================\n";
        
        $similarRoutes = [];
        foreach ($allRoutes as $route) {
            if (strpos($route['uri'], 'chat') !== false && strpos($route['uri'], 'start') !== false) {
                $similarRoutes[] = $route;
            }
        }
        
        if (!empty($similarRoutes)) {
            echo "Similar routes found:\n";
            foreach ($similarRoutes as $route) {
                echo "- {$route['methods']} {$route['uri']} -> {$route['action']}\n";
            }
        } else {
            echo "No similar routes found.\n";
        }
    }
    
    echo "\n5. MIDDLEWARE CHECK\n";
    echo "==================\n";
    
    // Check middleware for web-api routes
    foreach ($webApiChatRoutes as $route) {
        $routeObj = null;
        foreach (app('router')->getRoutes() as $r) {
            if ($r->uri() === $route['uri']) {
                $routeObj = $r;
                break;
            }
        }
        
        if ($routeObj) {
            $middleware = $routeObj->middleware();
            echo "Route: {$route['uri']}\n";
            echo "Middleware: " . (empty($middleware) ? 'none' : implode(', ', $middleware)) . "\n\n";
        }
    }
    
    echo "6. ENVIRONMENT CHECK\n";
    echo "===================\n";
    echo "Environment: " . app()->environment() . "\n";
    echo "Debug mode: " . (config('app.debug') ? 'enabled' : 'disabled') . "\n";
    echo "URL: " . config('app.url') . "\n\n";
    
    echo "7. RECOMMENDED FIXES\n";
    echo "===================\n";
    
    if (file_exists($routeCachePath)) {
        echo "1. Clear route cache:\n";
        echo "   php artisan route:clear\n\n";
    }
    
    echo "2. Clear all caches:\n";
    echo "   php artisan cache:clear\n";
    echo "   php artisan config:clear\n";
    echo "   php artisan view:clear\n\n";
    
    echo "3. Re-cache routes (production only):\n";
    echo "   php artisan route:cache\n\n";
    
    echo "4. Check web.php file for route definition around line 70-71\n\n";
    
    echo "5. Restart web server if using php artisan serve\n\n";
    
} catch (Exception $e) {
    echo "Error during debugging: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "=== END OF DEBUGGING ===\n";
